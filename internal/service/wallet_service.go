package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"time"

	"github.com/gagliardetto/solana-go"
	"go-starter/internal/models"
	"go-starter/internal/repository/mysql"
)

type WalletService interface {
	GetUserWallets(userID int64) ([]models.UserWallet, error)
	GetDefaultWallet(userID int64) (*models.UserWallet, error)
	SetDefaultWallet(userID int64, walletID int64) error
	AddWalletByPrivateKey(userID int64, privateKey, name string) (*models.UserWallet, error)
	RemoveWallet(userID int64, walletID int64) error
	GenerateWallet(userID int64, name string) (*models.UserWallet, string, error) // 返回钱包和私钥
	GetWalletSummary(userID int64, walletAddress string) (*models.WalletSummary, error)
	UpdateWalletName(userID int64, walletID int64, name string) error
}

type WalletServiceImpl struct {
	userRepo       mysql.UserRepository
	walletRepo     mysql.WalletRepository
	solanaService  SolanaService
	jupiterService JupiterService
	contextTimeout time.Duration
}

func NewWalletService(
	userRepo mysql.UserRepository,
	walletRepo mysql.WalletRepository,
	solanaService SolanaService,
	jupiterService JupiterService,
	timeout time.Duration,
) WalletService {
	return &WalletServiceImpl{
		userRepo:       userRepo,
		walletRepo:     walletRepo,
		solanaService:  solanaService,
		jupiterService: jupiterService,
		contextTimeout: timeout,
	}
}

func (w *WalletServiceImpl) GetUserWallets(userID int64) ([]models.UserWallet, error) {
	// 如果没有数据库连接，返回空列表
	if w.walletRepo == nil {
		return []models.UserWallet{}, nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
	defer cancel()

	return w.walletRepo.GetUserWallets(ctx, userID)
}

func (w *WalletServiceImpl) GetDefaultWallet(userID int64) (*models.UserWallet, error) {
	if w.walletRepo == nil {
		return nil, fmt.Errorf("no wallet found")
	}

	ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
	defer cancel()

	return w.walletRepo.GetDefaultWallet(ctx, userID)
}

func (w *WalletServiceImpl) SetDefaultWallet(userID int64, walletID int64) error {
	if w.walletRepo == nil {
		return fmt.Errorf("wallet repository not available")
	}

	ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
	defer cancel()

	return w.walletRepo.SetDefaultWallet(ctx, userID, walletID)
}

func (w *WalletServiceImpl) AddWalletByPrivateKey(userID int64, privateKey, name string) (*models.UserWallet, error) {
	// 从私钥恢复钱包
	walletInfo, err := w.recoverWalletFromPrivateKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("invalid private key: %w", err)
	}

	// 检查是否是用户的第一个钱包
	isFirstWallet := true
	if w.walletRepo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
		defer cancel()

		existingWallets, err := w.walletRepo.GetUserWallets(ctx, userID)
		if err == nil && len(existingWallets) > 0 {
			isFirstWallet = false
		}
	}

	// 创建钱包记录
	wallet := &models.UserWallet{
		UserID:     userID,
		Wallet:     walletInfo.Wallet,
		PrivateKey: privateKey, // 实际应用中需要加密存储
		Name:       name,
		IsDefault:  isFirstWallet, // 第一个钱包设为默认
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 保存到数据库
	if w.walletRepo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
		defer cancel()

		err := w.walletRepo.CreateWallet(ctx, wallet)
		if err != nil {
			return nil, fmt.Errorf("failed to save wallet: %w", err)
		}
	}

	return wallet, nil
}

func (w *WalletServiceImpl) recoverWalletFromPrivateKey(privateKeyStr string) (*models.UserWallet, error) {
	// 尝试解析私钥
	privateKey, err := solana.PrivateKeyFromBase58(privateKeyStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	// 获取公钥（钱包地址）
	publicKey := privateKey.PublicKey()

	wallet := &models.UserWallet{
		Wallet:    publicKey.String(),
		Name:      "导入钱包",
		IsDefault: false,
		IsActive:  true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return wallet, nil
}

func (w *WalletServiceImpl) RemoveWallet(userID int64, walletID int64) error {
	// 如果没有数据库连接，模拟成功
	if w.userRepo == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
	defer cancel()

	// 这里需要实现删除钱包的逻辑
	// 暂时删除主钱包
	return w.userRepo.SaveWallet(ctx, userID, "")
}

func (w *WalletServiceImpl) GenerateWallet(userID int64, name string) (*models.UserWallet, string, error) {
	// 生成新的Solana钱包
	account := solana.NewWallet()
	privateKey := account.PrivateKey.String()

	// 检查是否是用户的第一个钱包
	isFirstWallet := true
	if w.walletRepo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
		defer cancel()

		existingWallets, err := w.walletRepo.GetUserWallets(ctx, userID)
		if err == nil && len(existingWallets) > 0 {
			isFirstWallet = false
		}
	}

	wallet := &models.UserWallet{
		UserID:     userID,
		Wallet:     account.PublicKey().String(),
		PrivateKey: privateKey, // 实际应用中需要加密存储
		Name:       name,
		IsDefault:  isFirstWallet, // 第一个钱包设为默认
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 保存到数据库
	if w.walletRepo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), w.contextTimeout*time.Second)
		defer cancel()

		err := w.walletRepo.CreateWallet(ctx, wallet)
		if err != nil {
			return nil, "", fmt.Errorf("failed to save wallet: %w", err)
		}
	}

	return wallet, privateKey, nil
}

func (w *WalletServiceImpl) GetWalletSummary(userID int64, walletAddress string) (*models.WalletSummary, error) {
	// 获取SOL余额
	solBalance, err := w.solanaService.GetSOLBalance(walletAddress)
	if err != nil {
		log.Printf("Error getting SOL balance: %v", err)
		solBalance = 0
	}

	// 获取代币信息
	_, tokens, err := w.solanaService.GetBalance(walletAddress)
	if err != nil {
		log.Printf("Error getting tokens: %v", err)
		tokens = []models.TokenInfo{}
	}

	// 计算总价值
	var totalValue float64

	// SOL价格
	solPrice, err := w.jupiterService.GetTokenPrice("SOL")
	if err != nil {
		log.Printf("Error getting SOL price: %v", err)
		solPrice = 0
	}

	solValue := solBalance * solPrice
	totalValue += solValue

	// 处理代币信息，只获取当前价格和价值
	for i := range tokens {
		token := &tokens[i]

		// 获取代币价格
		price, err := w.jupiterService.GetTokenPrice(token.Symbol)
		if err != nil {
			log.Printf("Error getting price for %s: %v", token.Symbol, err)
			price = 0
		}

		token.Price = price
		token.Value = token.Balance * price
		totalValue += token.Value
	}

	summary := &models.WalletSummary{
		Address:    walletAddress,
		Name:       "主钱包",
		SOLBalance: solBalance,
		TotalValue: totalValue,
		TokenCount: len(tokens),
		Tokens:     tokens,
		UpdatedAt:  time.Now(),
	}

	return summary, nil
}

func (w *WalletServiceImpl) UpdateWalletName(userID int64, walletID int64, name string) error {
	// 这里需要实现更新钱包名称的逻辑
	// 暂时返回成功
	return nil
}

// generateRandomBytes 生成随机字节
func generateRandomBytes(length int) ([]byte, error) {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return nil, err
	}
	return bytes, nil
}

// generateRandomHex 生成随机十六进制字符串
func generateRandomHex(length int) (string, error) {
	bytes, err := generateRandomBytes(length)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
