package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"go-starter/internal/models"
)

type JupiterService interface {
	GetTokenPrice(symbol string) (float64, error)
	GetSwapQuote(inputMint, outputMint string, amount uint64, slippageBps int) (*models.SwapQuote, error)
	GetSwapTransaction(quote *models.SwapQuote, userPublicKey string) (*models.SwapTransaction, error)
	GetTokenList() ([]models.TokenInfo, error)
	GetTokenInfoByAddress(tokenAddress string) (*models.JupiterTokenInfo, error)
	CreateOrder(inputMint, outputMint string, amount uint64, taker string) (*models.JupiterOrderResponse, error)
	ExecuteOrder(signedTransaction, requestID string) (*models.JupiterExecuteResponse, error)
}

type JupiterServiceImpl struct {
	baseURL    string
	httpClient *http.Client
}

func NewJupiterService() JupiterService {
	return &JupiterServiceImpl{
		baseURL: "https://quote-api.jup.ag/v6",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (j *JupiterServiceImpl) GetTokenPrice(symbol string) (float64, error) {
	// 首先获取代币地址
	tokenAddress, err := j.getTokenAddressBySymbol(symbol)
	if err != nil {
		return 0, fmt.Errorf("failed to get token address: %w", err)
	}

	// 使用CoinGecko API获取价格
	url := fmt.Sprintf("https://api.coingecko.com/api/v3/simple/token_price/solana?contract_addresses=%s&vs_currencies=usd", tokenAddress)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch price: %w", err)
	}
	defer resp.Body.Close()

	var priceData map[string]map[string]float64
	if err := json.NewDecoder(resp.Body).Decode(&priceData); err != nil {
		return 0, fmt.Errorf("failed to decode price response: %w", err)
	}

	if tokenPrice, exists := priceData[strings.ToLower(tokenAddress)]; exists {
		if price, ok := tokenPrice["usd"]; ok {
			return price, nil
		}
	}

	return 0, fmt.Errorf("price not found for token %s", symbol)
}

func (j *JupiterServiceImpl) getTokenAddressBySymbol(symbol string) (string, error) {
	// 常见代币地址映射
	tokenAddresses := map[string]string{
		"SOL":  "So11111111111111111111111111111111111111112",
		"USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
		"USDT": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
		"RAY":  "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",
		"SRM":  "SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt",
	}

	if address, exists := tokenAddresses[strings.ToUpper(symbol)]; exists {
		return address, nil
	}

	// 如果不在预定义列表中，尝试从Jupiter代币列表获取
	tokens, err := j.GetTokenList()
	if err != nil {
		return "", err
	}

	for _, token := range tokens {
		if strings.EqualFold(token.Symbol, symbol) {
			return token.Address, nil
		}
	}

	return "", fmt.Errorf("token %s not found", symbol)
}

func (j *JupiterServiceImpl) GetTokenList() ([]models.TokenInfo, error) {
	url := "https://token.jup.ag/strict"

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token list: %w", err)
	}
	defer resp.Body.Close()

	var jupiterTokens []struct {
		Address  string `json:"address"`
		Symbol   string `json:"symbol"`
		Name     string `json:"name"`
		Decimals int    `json:"decimals"`
		LogoURI  string `json:"logoURI"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&jupiterTokens); err != nil {
		return nil, fmt.Errorf("failed to decode token list: %w", err)
	}

	var tokens []models.TokenInfo
	for _, jToken := range jupiterTokens {
		token := models.TokenInfo{
			Address:  jToken.Address,
			Symbol:   jToken.Symbol,
			Name:     jToken.Name,
			Decimals: jToken.Decimals,
		}
		tokens = append(tokens, token)
	}

	return tokens, nil
}

func (j *JupiterServiceImpl) GetSwapQuote(inputMint, outputMint string, amount uint64, slippageBps int) (*models.SwapQuote, error) {
	url := fmt.Sprintf("%s/quote?inputMint=%s&outputMint=%s&amount=%d&slippageBps=%d",
		j.baseURL, inputMint, outputMint, amount, slippageBps)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get swap quote: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter API returned status %d", resp.StatusCode)
	}

	var quote models.SwapQuote
	if err := json.NewDecoder(resp.Body).Decode(&quote); err != nil {
		return nil, fmt.Errorf("failed to decode swap quote: %w", err)
	}

	return &quote, nil
}

func (j *JupiterServiceImpl) GetSwapTransaction(quote *models.SwapQuote, userPublicKey string) (*models.SwapTransaction, error) {
	url := fmt.Sprintf("%s/swap", j.baseURL)

	requestBody := map[string]interface{}{
		"quoteResponse":    quote,
		"userPublicKey":    userPublicKey,
		"wrapAndUnwrapSol": true,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := j.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to get swap transaction: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter API returned status %d", resp.StatusCode)
	}

	var swapTx models.SwapTransaction
	if err := json.NewDecoder(resp.Body).Decode(&swapTx); err != nil {
		return nil, fmt.Errorf("failed to decode swap transaction: %w", err)
	}

	return &swapTx, nil
}

func (j *JupiterServiceImpl) GetTokenInfoByAddress(tokenAddress string) (*models.JupiterTokenInfo, error) {
	url := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/search?query=%s", tokenAddress)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter API returned status %d", resp.StatusCode)
	}

	var tokenResponse models.JupiterTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	if len(tokenResponse) == 0 {
		return nil, fmt.Errorf("token not found: %s", tokenAddress)
	}

	// 返回第一个匹配的代币信息
	return &tokenResponse[0], nil
}

func (j *JupiterServiceImpl) CreateOrder(inputMint, outputMint string, amount uint64, taker string) (*models.JupiterOrderResponse, error) {
	url := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/order?inputMint=%s&outputMint=%s&amount=%d&taker=%s",
		inputMint, outputMint, amount, taker)

	log.Printf("Creating Jupiter order: %s", url)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体以便调试
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("Jupiter API response status: %d, body: %s", resp.StatusCode, string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter Ultra API returned status %d: %s", resp.StatusCode, string(body))
	}

	var orderResponse models.JupiterOrderResponse
	if err := json.Unmarshal(body, &orderResponse); err != nil {
		return nil, fmt.Errorf("failed to decode order response: %w", err)
	}

	return &orderResponse, nil
}

func (j *JupiterServiceImpl) ExecuteOrder(signedTransaction, requestID string) (*models.JupiterExecuteResponse, error) {
	url := "https://lite-api.jup.ag/ultra/v1/execute"

	executeRequest := models.JupiterExecuteRequest{
		SignedTransaction: signedTransaction,
		RequestID:         requestID,
	}

	jsonData, err := json.Marshal(executeRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal execute request: %w", err)
	}

	resp, err := j.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to execute order: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter Ultra API returned status %d", resp.StatusCode)
	}

	var executeResponse models.JupiterExecuteResponse
	if err := json.NewDecoder(resp.Body).Decode(&executeResponse); err != nil {
		return nil, fmt.Errorf("failed to decode execute response: %w", err)
	}

	return &executeResponse, nil
}
